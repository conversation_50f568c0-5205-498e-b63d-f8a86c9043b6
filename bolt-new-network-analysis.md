# Bolt.new Network Analysis Documentation

## Overview
This document provides a comprehensive analysis of bolt.new's network architecture, API endpoints, and authentication mechanisms, specifically focused on intercepting the chat functionality for creating a Go API wrapper.

## Executive Summary
- **Target**: bolt.new chat functionality only (not full platform)
- **Authentication**: OAuth via StackBlitz (Google/GitHub providers)
- **Infrastructure**: React SPA + StackBlitz WebContainers + AI integration
- **Key Finding**: Chat functionality appears to be integrated with StackBlitz's backend services

## Authentication Architecture

### OAuth Providers
Bolt.new uses StackBlitz's authentication system with multiple OAuth providers:

#### Google OAuth
- **Client ID**: `655462982262-utviqnfkli8nvefabcvdbu3ajb8qcibo.apps.googleusercontent.com`
- **Redirect URI**: `https://stackblitz.com/api/users/auth/google_oauth2/callback`
- **Scope**: `https://www.googleapis.com/auth/userinfo.email`
- **Flow**: Standard OAuth 2.0 Authorization Code flow

#### GitHub OAuth  
- **Client ID**: `Iv1.a68538aa23f421cc`
- **Redirect URI**: `https://stackblitz.com/api/users/auth/github_app/callback`
- **Scope**: `user:email`
- **Flow**: Standard OAuth 2.0 Authorization Code flow

### Authentication Flow
1. User clicks "Sign in with Google/GitHub"
2. Redirects to OAuth provider (Google/GitHub)
3. User completes authentication
4. Provider redirects to StackBlitz callback URL
5. StackBlitz processes OAuth response
6. User is redirected back to bolt.new with session

## Core API Endpoints

### Discovered Endpoints
| Endpoint | Method | Status | Purpose | Notes |
|----------|--------|--------|---------|-------|
| `/api/secrets` | GET | 401 | Authentication/API keys | Requires authentication |
| `/api/analytics` | POST | 200 | Usage tracking | Analytics data |
| `/api/sessions` | POST | 200 | Session management | Created during OAuth flow |

### Expected Chat Endpoints (Not Yet Discovered)
Based on typical AI chat implementations, we expect:
- `POST /api/chat` - Send chat messages
- `GET /api/chat/history` - Retrieve chat history  
- `POST /api/chat/stream` - Streaming chat responses
- `GET /api/projects/{id}/chat` - Project-specific chat

## Frontend Architecture

### Technology Stack
- **Framework**: React SPA
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + custom CSS
- **State Management**: React hooks/context
- **WebContainers**: StackBlitz WebContainers API

### Key JavaScript Bundles
```
/assets/root-*.js           - Main application bundle
/assets/components-*.js     - UI components
/assets/analytics.client-*.js - Analytics tracking
/assets/login-*.js          - Authentication logic
/assets/api-*.js            - API client utilities
```

## WebContainers Integration

### StackBlitz WebContainers
Bolt.new heavily integrates with StackBlitz's WebContainers technology:

- **Base URL**: `https://stackblitz.com/headless`
- **Version**: `1.6.1-internal.2`
- **Purpose**: Browser-based Node.js runtime for code execution
- **WASM Files**: Multiple WebAssembly modules for filesystem, bindings, storage

### WebContainer Endpoints
```
https://w-credentialless-staticblitz.com/webcontainer.*.js
https://w-credentialless-staticblitz.com/fetch.worker.*.js  
https://w-credentialless-staticblitz.com/fs_bg.*.wasm
https://w-credentialless-staticblitz.com/internal_bindings_bg.*.wasm
```

## Network Traffic Patterns

### Initial Page Load
1. **Static Assets**: CSS, JS bundles, fonts
2. **Analytics**: Google Analytics, Facebook Pixel, HubSpot
3. **WebContainers**: WASM modules and workers
4. **API Calls**: `/api/secrets` (401), `/api/analytics`

### Authentication Flow
1. **Login Dialog**: Triggered by user action
2. **OAuth Redirect**: To Google/GitHub
3. **Session Creation**: `POST /api/sessions`
4. **Analytics**: Track login events

### Expected Chat Flow (To Be Analyzed)
1. **Chat Initiation**: User types message
2. **API Request**: `POST /api/chat` with message
3. **AI Processing**: Backend processes with AI model
4. **Response Stream**: Streaming or batch response
5. **WebContainer Updates**: Code generation/execution

## Third-Party Integrations

### Analytics & Tracking
- **Google Analytics**: `G-SLJ4P1NJFR`
- **Google Ads**: `AW-16735431429`
- **Facebook Pixel**: `9472948572788130`
- **HubSpot**: `45403856`
- **Reddit Pixel**: `a2_ghqj0ktog57o`
- **Twitter**: Conversion tracking

### External Services
- **Chameleon**: User onboarding (`fast.chameleon.io`)
- **Google Fonts**: Inter font family
- **StackBlitz**: Core infrastructure and WebContainers

## Security Considerations

### CORS & Security Headers
- Uses `credentialless` mode for WebContainers
- Cross-origin isolation for WASM execution
- Secure OAuth implementation

### Authentication Security
- Standard OAuth 2.0 flows
- HTTPS everywhere
- Session-based authentication post-OAuth

## Next Steps for API Interception

### 1. Complete Authentication
- Finish Google OAuth flow
- Capture session tokens/cookies
- Analyze authenticated API calls

### 2. Chat Functionality Analysis
- Initiate chat conversation
- Monitor network requests during chat
- Identify chat API endpoints and formats

### 3. API Reverse Engineering
- Document request/response formats
- Identify required headers and authentication
- Map chat functionality to API calls

### 4. Go API Wrapper Design
- Design Go structs for API requests/responses
- Implement OAuth flow in Go
- Create chat API client
- Add session management

## Implementation Strategy

### Approach 1: Proxy Interception
- Create HTTP proxy to intercept bolt.new requests
- Capture and replay chat API calls
- Minimal reverse engineering required

### Approach 2: Direct API Integration  
- Reverse engineer chat API completely
- Implement direct API client in Go
- Full control over requests/responses

### Recommended: Hybrid Approach
- Use proxy for initial discovery and testing
- Gradually replace with direct API calls
- Maintain compatibility with bolt.new's API

## Go API Wrapper Implementation Plan

### 1. Cookie Session Authentication
Since you prefer cookie session authentication, here's the approach:

```go
type BoltClient struct {
    httpClient *http.Client
    baseURL    string
    cookies    []*http.Cookie
    orgID      string
}

// Authenticate using Google OAuth cookies
func (c *BoltClient) SetCookies(cookies []*http.Cookie) {
    c.cookies = cookies
}
```

### 2. Core Chat API Implementation

```go
type ChatRequest struct {
    Message     string `json:"message"`
    ProjectID   string `json:"projectId,omitempty"`
    Context     string `json:"context,omitempty"`
}

type ChatResponse struct {
    Response    string `json:"response"`
    ProjectID   string `json:"projectId"`
    Files       []File `json:"files,omitempty"`
    Actions     []Action `json:"actions,omitempty"`
}

func (c *BoltClient) SendChat(req ChatRequest) (*ChatResponse, error) {
    // POST https://bolt.new/api/chat
    // Include cookies for authentication
    // Handle streaming response if needed
}
```

### 3. Required Headers & Authentication
Based on network analysis, include these headers:
- `Cookie`: Session cookies from Google OAuth
- `Content-Type`: `application/json`
- `User-Agent`: Browser user agent
- `Referer`: `https://bolt.new/`
- `Origin`: `https://bolt.new`

### 4. API Wrapper Endpoints
Your Go server should expose:
```
POST /api/v1/chat          # Main chat endpoint
GET  /api/v1/projects      # List projects
GET  /api/v1/projects/{id} # Get project details
POST /api/v1/auth/login    # Handle OAuth flow
GET  /api/v1/health        # Health check
```

## 🎉 MAJOR BREAKTHROUGH - Chat API Discovered!

### Chat API Endpoint
**`POST https://bolt.new/api/chat`** ✅ **CONFIRMED WORKING**

This is the **primary endpoint** for bolt.new's chat functionality that we need to intercept!

### Complete Authenticated API Endpoints

| Endpoint | Method | Status | Purpose | Team Account |
|----------|--------|--------|---------|--------------|
| **`POST /api/chat`** | POST | **200** ✅ | **MAIN CHAT API** | ✅ |
| **`GET /api/chats?organization=22851`** | GET | **200** ✅ | Chat history for org | ✅ |
| **`GET /api/secrets`** | GET | **200** ✅ | API keys/secrets | ✅ |
| **`GET /api/token`** | GET | **200** ✅ | Auth token | ✅ |
| **`GET /api/token/cors`** | GET | **200** ✅ | CORS token | ✅ |
| **`POST /api/template`** | POST | **200** ✅ | Project template | ✅ |
| **`POST /api/analytics`** | POST | **200** ✅ | Usage tracking | ✅ |
| **`POST /api/chameleon`** | POST | **200** ✅ | User onboarding | ✅ |
| **`GET /api/stripe/secrets/{projectId}`** | GET | **200** ✅ | Stripe integration | ✅ |
| **`GET /api/project/integrations/supabase/{projectId}`** | GET | **404** | Supabase integration | ✅ |

### Project Creation Flow
1. **`POST /api/template`** - Creates project template
2. **`POST /api/projects/sb1/fork`** - Forks StackBlitz project
3. **`POST /api/chat`** - Sends chat message to AI
4. **WebContainer initialization** - Sets up browser environment

### Team Account Details
- **Organization ID**: `22851`
- **Project ID**: `sb1-88qhk3qm` (auto-generated)
- **Project URL**: `https://bolt.new/~/sb1-88qhk3qm`
- **Token Usage**: "10.6M monthly tokens remaining"

## Current Status
- ✅ Authentication flow analysis complete
- ✅ Core API endpoints identified
- ✅ WebContainers integration understood
- ✅ Google OAuth complete
- ✅ **Chat functionality analysis COMPLETE** 🎉
- ⏳ Go API wrapper implementation pending
